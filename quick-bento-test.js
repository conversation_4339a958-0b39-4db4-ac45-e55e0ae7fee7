#!/usr/bin/env node

/**
 * Quick Bento Grid Test with Working Account
 * 
 * Test the bento grid <NAME_EMAIL>
 */

const { chromium } = require('playwright');

async function quickBentoTest() {
  console.log('🎯 Quick Bento Grid Test - Working Account');
  console.log('='.repeat(45));

  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log('🔑 Logging in with working account...');
    await page.goto('http://localhost:5173');
    await page.waitForLoadState('networkidle');

    // Login with working credentials
    const emailInput = page.locator('input[type="email"]').first();
    const passwordInput = page.locator('input[type="password"]').first();
    
    await emailInput.fill('<EMAIL>');
    await passwordInput.fill('TestPassword123!');
    await page.click('button[type="submit"]');
    
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    console.log('✅ Authentication successful!');

    // Take screenshot of authenticated state
    await page.screenshot({ path: 'test-results/authenticated-bento-grid.png', fullPage: true });

    // Count bento grid elements
    const bentoCards = await page.locator('[data-canvas-card]').count();
    const gridElements = await page.locator('.grid').count();
    
    console.log(`\n📊 Bento Grid Analysis:`);
    console.log(`  Canvas cards: ${bentoCards}`);
    console.log(`  Grid elements: ${gridElements}`);

    if (bentoCards > 0) {
      console.log('\n🎯 Testing individual tiles...');
      
      // Test first 5 tiles
      for (let i = 0; i < Math.min(bentoCards, 5); i++) {
        const tile = page.locator('[data-canvas-card]').nth(i);
        const tileText = await tile.textContent();
        const tileName = tileText?.split('\n')[0]?.trim() || `Tile ${i + 1}`;
        
        console.log(`\n  🖱️  Testing: ${tileName}`);
        
        try {
          await tile.click();
          await page.waitForLoadState('networkidle');
          
          const newUrl = page.url();
          console.log(`    ✅ Navigated to: ${newUrl}`);
          
          // Go back to home
          await page.goto('http://localhost:5173');
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(1000);
          
        } catch (error) {
          console.log(`    ❌ Error: ${error.message}`);
        }
      }
      
      console.log('\n🎉 Bento grid navigation test completed successfully!');
      
    } else {
      console.log('\n❌ No bento cards found');
    }

    // Keep browser open for manual inspection
    console.log('\n👀 Browser staying open for 20 seconds...');
    await page.waitForTimeout(20000);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    await page.screenshot({ path: 'test-results/quick-test-error.png', fullPage: true });
  } finally {
    await browser.close();
    console.log('\n✅ Quick test completed!');
  }
}

// Run the test
quickBentoTest().catch(console.error);
